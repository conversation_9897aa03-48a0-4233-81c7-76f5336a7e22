import api from './api';

const rbacApi = {
  // Get all available roles
  getRoles: async () => {
    return await api.get('/auth/roles');
  },

  // Get current user permissions
  getUserPermissions: async () => {
    return await api.get('/auth/permissions');
  },

  // Update user role and topic assignments
  updateUserRole: async (userId, roleData) => {
    return await api.put(`/auth/users/${userId}/role`, roleData);
  },

  // Register new user with role and topic assignments
  registerUser: async (userData) => {
    return await api.post('/auth/register', userData);
  }
};

export default rbacApi;
