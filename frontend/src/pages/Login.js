import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Container,
  IconButton,
  InputAdornment,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  AccountCircle,
  Lock,
} from '@mui/icons-material';
import { useMutation } from 'react-query';
import { useNavigate, useLocation } from 'react-router-dom';
import toast from 'react-hot-toast';
import { authApi } from '../services/api';

const Login = ({ onLogin }) => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');

  const navigate = useNavigate();
  const location = useLocation();
  const from = location.state?.from?.pathname || '/';
  
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  const loginMutation = useMutation(authApi.login, {
    onSuccess: (data) => {
      // Store token in localStorage
      localStorage.setItem('token', data.token);
      localStorage.setItem('user', JSON.stringify(data.user));

      // Call parent onLogin function
      if (onLogin) {
        onLogin(data.user, data.token);
      }
      
      toast.success('Login successful!');
      navigate(from, { replace: true });
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.message || error.message || 'Login failed';
      setError(errorMessage);
      toast.error(errorMessage);
    },
  });

  const handleChange = (field) => (event) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleSubmit = (event) => {
    event.preventDefault();
    
    if (!formData.username.trim() || !formData.password.trim()) {
      setError('Please enter both username and password');
      return;
    }

    loginMutation.mutate(formData);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(prev => !prev);
  };

  return (
    <Container component="main" maxWidth="sm">
      <Box
        sx={{
          marginTop: { xs: 4, sm: 8 },
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          minHeight: '100vh',
          py: { xs: 2, sm: 4 },
        }}
      >
        {/* Header */}
        <Box sx={{ mb: { xs: 2, sm: 4 }, textAlign: 'center' }}>
          <Typography 
            component="h1" 
            variant={isSmallScreen ? "h4" : "h3"} 
            color="primary" 
            gutterBottom
            sx={{ 
              fontSize: { xs: '1.75rem', sm: '3rem' },
              fontWeight: 700,
            }}
          >
            Kafka Dashboard
          </Typography>
          <Typography 
            variant={isSmallScreen ? "h6" : "h5"} 
            color="text.secondary"
            sx={{ 
              fontSize: { xs: '1.125rem', sm: '1.5rem' },
              fontWeight: 500,
            }}
          >
            PolicyBazaar
          </Typography>
        </Box>

        <Card sx={{ 
          width: '100%', 
          boxShadow: 3,
          maxWidth: { xs: '100%', sm: 400 },
        }}>
          <CardContent sx={{ p: { xs: 3, sm: 4 } }}>
            <Box sx={{ textAlign: 'center', mb: { xs: 2, sm: 3 } }}>
              <Typography 
                component="h2" 
                variant={isSmallScreen ? "h5" : "h4"} 
                gutterBottom
                sx={{ 
                  fontSize: { xs: '1.25rem', sm: '2.125rem' },
                  fontWeight: 600,
                }}
              >
                Sign In
              </Typography>
              <Typography 
                variant="body2" 
                color="text.secondary"
                sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}
              >
                Enter your credentials to access the dashboard
              </Typography>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: { xs: 2, sm: 3 } }}>
                <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                  {error}
                </Typography>
              </Alert>
            )}

            <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
              <TextField
                margin="normal"
                required
                fullWidth
                id="username"
                label="Username or Email"
                name="username"
                autoComplete="username"
                autoFocus
                value={formData.username}
                onChange={handleChange('username')}
                disabled={loginMutation.isLoading}
                size={isSmallScreen ? "small" : "medium"}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <AccountCircle color="action" fontSize={isSmallScreen ? "small" : "medium"} />
                    </InputAdornment>
                  ),
                }}
              />
              
              <TextField
                margin="normal"
                required
                fullWidth
                name="password"
                label="Password"
                type={showPassword ? 'text' : 'password'}
                id="password"
                autoComplete="current-password"
                value={formData.password}
                onChange={handleChange('password')}
                disabled={loginMutation.isLoading}
                size={isSmallScreen ? "small" : "medium"}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Lock color="action" fontSize={isSmallScreen ? "small" : "medium"} />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={togglePasswordVisibility}
                        edge="end"
                        size={isSmallScreen ? "small" : "medium"}
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                sx={{ 
                  mt: { xs: 2, sm: 3 }, 
                  mb: { xs: 1, sm: 2 }, 
                  py: { xs: 1, sm: 1.5 },
                  fontSize: { xs: '0.875rem', sm: '1rem' },
                  fontWeight: 600,
                }}
                disabled={loginMutation.isLoading}
                size={isSmallScreen ? "medium" : "large"}
              >
                {loginMutation.isLoading ? (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CircularProgress size={isSmallScreen ? 16 : 20} color="inherit" />
                    <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                      Signing in...
                    </Typography>
                  </Box>
                ) : (
                  'Sign In'
                )}
              </Button>

              {/* <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  Default admin credentials: admin / admin123
                </Typography>
              </Box> */}
            </Box>
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
};

export default Login; 