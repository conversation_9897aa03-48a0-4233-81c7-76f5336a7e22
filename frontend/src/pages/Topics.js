import React, { useState, useEffect } from 'react';
import { usePermissions, PERMISSIONS } from '../contexts/PermissionsContext';
import {
  Box,
  Typography,
  <PERSON>ton,
  Card,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  InputAdornment,
  useTheme,
  useMediaQuery,
  Stack,
} from '@mui/material';
import {
  Add,
  // Edit,
  Delete,
  Visibility,
  Topic as TopicIcon,
  Settings,
  Search,
  Clear,
  Message as MessageIcon,
  Refresh,
} from '@mui/icons-material';
import { useMutation, useQueryClient } from 'react-query';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import { topicsApi } from '../services/api';
// import { useDebounce } from '../hooks/useDebounce';
import { useTopics } from '../hooks/useTopics';
import { FixedSizeList as List } from 'react-window';



const useFilteredTopics = (topics = [], searchTerm = '') => {
  const [filtered, setFiltered] = useState(topics);

  useEffect(() => {
    if (!searchTerm || !topics.length) {
      setFiltered(topics);
      return;
    }

    const timeout = setTimeout(() => {
      const lower = searchTerm.toLowerCase();
      const result = topics.filter((t) =>
        t.name.toLowerCase().includes(lower)
      );
      setFiltered(result);
    }, 100);

    return () => clearTimeout(timeout);
  }, [topics, searchTerm]);

  return filtered;
};

const CreateTopicDialog = ({ open, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    name: '',
    numPartitions: 1,
    replicationFactor: 1,
    configs: [],
  });

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = () => {
    if (!formData.name.trim()) {
      toast.error('Topic name is required');
      return;
    }
    onSubmit(formData);
    setFormData({ name: '', numPartitions: 1, replicationFactor: 1, configs: [] });
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Create New Topic</DialogTitle>
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
          <TextField
            fullWidth
            label="Topic Name"
            value={formData.name}
            onChange={(e) => handleChange('name', e.target.value)}
            required
          />
          <TextField
            fullWidth
            label="Number of Partitions"
            type="number"
            value={formData.numPartitions}
            onChange={(e) => handleChange('numPartitions', parseInt(e.target.value))}
            inputProps={{ min: 1 }}
          />
          <TextField
            fullWidth
            label="Replication Factor"
            type="number"
            value={formData.replicationFactor}
            onChange={(e) => handleChange('replicationFactor', parseInt(e.target.value))}
            inputProps={{ min: 1 }}
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSubmit} variant="contained">
          Create Topic
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const TopicCard = ({ topic, onDelete, onView, onConfigure }) => {
  const [isLoadingCount, setIsLoadingCount] = useState(false);
  const [messageCount, setMessageCount] = useState(topic.totalMessages);
  const [partitionDetails, setPartitionDetails] = useState(topic.partitionDetails);

  const theme = useTheme();
  const { hasPermission, isSuperAdmin, isTopicManager } = usePermissions();

  const formatNumber = (num) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
    if (num >= 1000) return (num / 1000).toFixed(1) + "K";
    return num?.toString() || "0";
  };

  const handleLoadMessageCount = async () => {
    if (messageCount !== undefined || isLoadingCount) return;

    setIsLoadingCount(true);
    try {
      const response = await topicsApi.getMessageCount(topic.name);
      setMessageCount(response.data.totalMessages);
      setPartitionDetails(response.data.partitionDetails);
      toast.success(`Message count loaded for ${topic.name}`);
    } catch (error) {
      toast.error(`Failed to load message count: ${error.message}`);
    } finally {
      setIsLoadingCount(false);
    }
  };

  return (
    <Card
      sx={{
        px: 2,
        py: 2,
        display: "flex",
        flexDirection: "column",
        gap: 1.5,
        justifyContent: "space-between",
        '&:hover': {
          boxShadow: theme.shadows[4],
          transform: "translateY(-1px)",
          transition: "all 0.2s ease-in-out",
        },
      }}
    >
      {/* Top row: icon + name + actions */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          flexWrap: "wrap",
          gap: 1,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1, minWidth: 0 }}>
          <TopicIcon sx={{ color: "primary.main", fontSize: 28 }} />
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 600,
              fontSize: { xs: "1rem", sm: "1.125rem" },
              overflow: "hidden",
              whiteSpace: "nowrap",
              textOverflow: "ellipsis",
              maxWidth: "240px",
            }}
          >
            {topic.name}
          </Typography>
        </Box>

        {/* Action buttons - based on user permissions */}
        <Stack direction="row" spacing={0.5}>
          {/* View button - available to all users with topic access */}
          <IconButton size="small" onClick={() => onView(topic.name)}>
            <Visibility fontSize="small" />
          </IconButton>

          {/* Delete button - only for Super Admin and Topic Managers with access to this topic */}
          {(isSuperAdmin() || isTopicManager()) && (
            <IconButton size="small" onClick={() => onDelete(topic.name)} color="error">
              <Delete fontSize="small" />
            </IconButton>
          )}

          {/* Configure button - only for Super Admin and Topic Managers with access to this topic */}
          {(isSuperAdmin() || isTopicManager()) && (
            <IconButton size="small" onClick={() => onConfigure(topic.name)}>
              <Settings fontSize="small" />
            </IconButton>
          )}
        </Stack>
      </Box>

      {/* Middle row: info chips */}
      <Stack
        direction="row"
        spacing={1}
        flexWrap="wrap"
        useFlexGap
        alignItems="center"
      >
        <Chip label={`${topic.partitions} partitions`} size="small" variant="outlined" />
        <Chip
          label={`${partitionDetails?.length || 0} replicas`}
          size="small"
          variant="outlined"
          color="secondary"
        />
        {messageCount !== undefined ? (
          <Chip
            label={`Messages: ${formatNumber(messageCount)}`}
            size="small"
            variant="outlined"
            color="success"
            icon={<MessageIcon fontSize="small" />}
          />
        ) : (
          <Button
            size="small"
            variant="outlined"
            startIcon={isLoadingCount ? <CircularProgress size={14} /> : <Refresh />}
            onClick={handleLoadMessageCount}
            sx={{
              fontSize: "0.75rem",
              px: 1.5,
              minWidth: "auto",
              whiteSpace: "nowrap",
            }}
          >
            {isLoadingCount ? "Loading..." : "Load Count"}
          </Button>
        )}
      </Stack>
    </Card>
  );
};

const Topics = () => {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [topicToDelete, setTopicToDelete] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  const { hasPermission, isSuperAdmin, isTopicManager, getAccessibleTopics } = usePermissions();

  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  // Use the shared topics hook for consistent caching
  const { data: topics, isLoading } = useTopics();

  // Filter topics based on user permissions
  const accessibleTopics = getAccessibleTopics(topics || []);
  const filteredTopics = useFilteredTopics(accessibleTopics, searchTerm);


  const createMutation = useMutation(topicsApi.create, {
    onSuccess: () => {
      toast.success('Topic created successfully');
      queryClient.invalidateQueries('topics');
      setCreateDialogOpen(false);
    },
    onError: (error) => {
      toast.error(`Error creating topic: ${error.message}`);
    },
  });

  const deleteMutation = useMutation(topicsApi.delete, {
    onSuccess: () => {
      toast.success('Topic deleted successfully');
      queryClient.invalidateQueries('topics');
      setDeleteConfirmOpen(false);
      setTopicToDelete(null);
    },
    onError: (error) => {
      toast.error(`Error deleting topic: ${error.message}`);
    },
  });

  const handleCreateTopic = (topicData) => {
    createMutation.mutate(topicData);
  };

  const handleDeleteTopic = (topicName) => {
    setTopicToDelete(topicName);
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = () => {
    if (topicToDelete) {
      deleteMutation.mutate(topicToDelete);
    }
  };

  const handleViewTopic = (topicName) => {
    navigate(`/topics/${topicName}`);
  };

  const handleConfigureTopic = (topicName) => {
    navigate(`/topics/${topicName}`, { state: { activeTab: 3 } });
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ 
        display: 'flex', 
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between', 
        alignItems: { xs: 'stretch', sm: 'center' }, 
        mb: { xs: 2, sm: 4 },
        gap: { xs: 2, sm: 0 }
      }}>
        <Typography 
          variant={isSmallScreen ? "h5" : "h4"}
          sx={{ 
            fontSize: { xs: '1.5rem', sm: '2.125rem' },
            fontWeight: 600,
          }}
        >
          Topics
        </Typography>
        {/* Only Super Admins can create topics */}
        {isSuperAdmin() && (
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setCreateDialogOpen(true)}
            size={isSmallScreen ? "small" : "medium"}
            fullWidth={isSmallScreen}
          >
            Create Topic
          </Button>
        )}
      </Box>

      {/* Performance Notice */}
      {/* <Alert severity="info" sx={{ mb: { xs: 2, sm: 3 } }}>
        <Typography variant="body2" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
          <strong>Performance Note:</strong> Message counts are loaded on-demand to improve page load speed. 
          Click "Load Count" on any topic card to see its message statistics.
        </Typography>
      </Alert> */}

      {/* Search Bar */}
      <Box sx={{ mb: { xs: 2, sm: 3 } }}>
        <TextField
          fullWidth
          placeholder="Search topics..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
            endAdornment: searchTerm && (
              <InputAdornment position="end">
                <IconButton
                  aria-label="clear search"
                  onClick={() => setSearchTerm('')}
                  edge="end"
                  size="small"
                >
                  <Clear />
                </IconButton>
              </InputAdornment>
            ),
          }}
          variant="outlined"
          size={isSmallScreen ? "small" : "medium"}
        />
      </Box>

      {/* Results Info */}
      {searchTerm && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            {filteredTopics.length} topic(s) found for "{searchTerm}"
          </Typography>
        </Box>
      )}

      {topics?.data?.length === 0 ? (
        <Alert severity="info" sx={{ mb: 2 }}>
          No topics found. Create your first topic to get started.
        </Alert>
      ) : filteredTopics.length === 0 && searchTerm ? (
        <Alert severity="info" sx={{ mb: 2 }}>
          No topics found matching "{searchTerm}". Try a different search term.
        </Alert>
      ) : (
        <List
          height={Math.min(900, window.innerHeight - 300)}
          itemCount={filteredTopics.length}
          itemSize={isSmallScreen ? 140 : 130}
          width="100%"
        >
          {({ index, style }) => {
            const topic = filteredTopics[index];
            return (
              <div style={style} key={topic.name}>
                <TopicCard
                  topic={topic}
                  onView={handleViewTopic}
                  onDelete={handleDeleteTopic}
                  onConfigure={handleConfigureTopic}
                />
              </div>
            );
          }}
        </List>
      )}

      <CreateTopicDialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        onSubmit={handleCreateTopic}
      />

      <Dialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete topic "{topicToDelete}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Topics; 